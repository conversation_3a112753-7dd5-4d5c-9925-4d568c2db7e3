import oracledb

# Build DSN
dsn = oracledb.makedsn("10.199.104.127", 1521, service_name="scrubbing")

# Connect
conn = oracledb.connect(user="READ_ONLY", password="READ_ONLY", dsn=dsn)

def get_doc_id_by_url(url):
    with conn.cursor() as cur:
        cur.execute("""
            SELECT doc.id
            FROM automation2.document@auto doc
            JOIN automation2.pdf@auto pdf ON doc.pdf_id = pdf.id
            WHERE pdf.se_url = :url
        """, [url])
        row = cur.fetchone()
        return row[0] if row else None   # return None if not found

url_1 = 'http://download.siliconexpert.com/pdfs2/2025/6/17/11/34/37/1241618801/ech_/manual/skupage.014626.pdf'
url_2 = 'http://download.siliconexpert.com/pdfs2/2025/9/19/12/10/5/910944574/ech_/manual/skupage.014626.pdf'

doc1 = get_doc_id_by_url(url_1)
doc2 = get_doc_id_by_url(url_2)

if doc1 and doc2:
    with conn.cursor() as cur:
        cur.execute("""
            SELECT *
            FROM automation2.new_compare_pdfs@auto
            WHERE rev_doc_id = :doc1
              AND cm_doc_id = :doc2
        """, [doc1, doc2])

        for row in cur:
            print(row)
else:
    print("One of the documents was not found.")
