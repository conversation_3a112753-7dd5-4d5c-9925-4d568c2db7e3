#!/usr/bin/env python3
"""
Startup script for the Oracle Database URL Query Tool
"""

import sys
import os
from app import app

def check_dependencies():
    """Check if all required dependencies are installed"""
    try:
        import flask
        import oracledb
        print("✓ All dependencies are installed")
        return True
    except ImportError as e:
        print(f"✗ Missing dependency: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def check_database_connection():
    """Test database connection"""
    try:
        from database import get_db_manager
        db = get_db_manager()
        print("✓ Database connection successful")
        return True
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        print("Please check your database configuration in database.py")
        return False

def main():
    """Main startup function"""
    print("=" * 50)
    print("Oracle Database URL Query Tool")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check database connection
    if not check_database_connection():
        print("\nWarning: Database connection failed. The application may not work properly.")
        response = input("Do you want to continue anyway? (y/N): ")
        if response.lower() != 'y':
            sys.exit(1)
    
    print("\n" + "=" * 50)
    print("Starting Flask application...")
    print("Access the application at: http://localhost:5000")
    print("Press Ctrl+C to stop the server")
    print("=" * 50 + "\n")
    
    # Start the Flask application
    try:
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n\nShutting down gracefully...")
    except Exception as e:
        print(f"\nError starting application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
