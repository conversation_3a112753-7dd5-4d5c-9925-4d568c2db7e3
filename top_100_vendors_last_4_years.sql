SELECT*FROM(
    SELECT 
        VENDOR_CODE,COUNT(*)
    FROM 
        CM.TBL_PDF_STATIC@DB1CONs
    WHERE 
        DELIVERY_DATE LIKE '%2022%' OR
        DELIVERY_DATE LIKE '%2023%' OR
        DELIVERY_DATE LIKE '%2024%' OR
        DELIVERY_DATE LIKE '%2025%'
        AND VE
    GROUP BY VENDOR_CODE
    ORDER BY COUNT(*) DESC)
WHERE ROWNUM <= 100;

SELECT *
FROM (
    SELECT 
        VENDOR_CODE,
        COUNT(*) AS vendor_count
    FROM 
        CM.TBL_PDF_STATIC@DB1CONs
    WHERE 
        DELIVERY_DATE LIKE '%2022%' OR
        DELIVERY_DATE LIKE '%2023%' OR
        DELIVERY_DATE LIKE '%2024%' OR
        DELIVERY_DATE LIKE '%2025%'
    GROUP BY VENDOR_CODE
    ORDER BY COUNT(*) DESC
) 
WHERE ROWNUM <= 100;


SELECT PDF_CM, PDF_LATEST, VENDOR_CODE FROM CM.TBL_PDF_STATIC@DB1CONS
WHERE   (DELIVERY_DATE LIKE '%2022%' OR
        DELIVERY_DATE LIKE '%2023%' OR
        DELIVERY_DATE LIKE '%2024%' OR
        DELIVERY_DATE LIKE '%2025%') AND VENDOR_CODE IN ('RAFIG', 'EMTEC');


SELECT S.PDF_CM, S.PDF_LATEST, S.VENDOR_CODE, C.COMPARE_STATUS, C.COMPARE_COMMENT, C.PN_STATUS, C.DATA_CHANGE, C.CHANGE_FEATURES
FROM CM.TBL_PDF_STATIC@DB1CONS S
JOIN importer.TBL_PDF_COMPARE@db1cons C
on S.CM_PDF_ID = C.CM_PDF_ID AND S.LATEST_PDF_ID = C.LATEST_PDF_ID
WHERE (C.COMPARE_STATUS = 'Done' AND C.PN_STATUS = 'Equal Parts' AND C.CHANGE_FEATURES IS NOT NULL AND S.VENDOR_CODE IN ('EMTEC', 'RAFIG', 'SWACO', 'TRL', 'ECH'))
AND(S.DELIVERY_DATE LIKE '%2022%' OR
        S.DELIVERY_DATE LIKE '%2023%' OR
        S.DELIVERY_DATE LIKE '%2024%' OR
        S.DELIVERY_DATE LIKE '%2025%');


SELECT  * FROM CM.TBL_PDF_STATIC@DB1CONS S; --LATEST_PDF_ID|LATEST_PDF_ID
SELECT  * FROM importer.TBL_PDF_COMPARE@db1cons C;--CM_PDF_ID|LATEST_PDF_ID,
 


select * from cm.XLP_SE_COMPONENT;
select * from cm.XLP_SE_PL;
select * from cm.PDF_TABLE;
select * from cm.XLP_SE_MANUFACTURER;
select * from cm.tbl_full_acquisition;
select * from cm.TBL_PART_ACQUISITION;
select * from STAR_GRADE.PRIORITY_PARTS_COVERAGE;