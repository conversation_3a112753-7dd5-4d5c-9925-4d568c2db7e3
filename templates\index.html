{% extends "base.html" %}

{% block title %}Oracle URL Query Tool - Home{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="text-center mb-4">
            <i class="fas fa-search me-2"></i>
            Oracle Database URL Query Tool
        </h1>
        <p class="text-center text-muted mb-5">
            Query Oracle database using URL pairs - upload a file or enter URLs manually
        </p>
    </div>
</div>

<div class="row">
    <!-- Manual Input Option -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100 shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-keyboard me-2"></i>
                    Manual URL Input
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text text-muted">
                    Enter two URLs manually for a quick single query.
                </p>
                
                <form method="POST" action="{{ url_for('manual_query') }}" id="manualForm">
                    <div class="mb-3">
                        <label for="url1" class="form-label">
                            <i class="fas fa-link me-1"></i>
                            URL 1
                        </label>
                        <input type="url" class="form-control" id="url1" name="url1" 
                               placeholder="https://example.com/document1.pdf" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="url2" class="form-label">
                            <i class="fas fa-link me-1"></i>
                            URL 2
                        </label>
                        <input type="url" class="form-control" id="url2" name="url2" 
                               placeholder="https://example.com/document2.pdf" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>
                        Query Database
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- File Upload Option -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100 shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-upload me-2"></i>
                    File Upload
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text text-muted">
                    Upload a text file containing multiple URL pairs for batch processing.
                </p>
                
                <form method="POST" action="{{ url_for('file_upload') }}" 
                      enctype="multipart/form-data" id="uploadForm">
                    
                    <div class="mb-3">
                        <label for="delimiter" class="form-label">
                            <i class="fas fa-cog me-1"></i>
                            File Format
                        </label>
                        <select class="form-select" id="delimiter" name="delimiter">
                            <option value="auto">Auto-detect</option>
                            <option value="comma">Comma-separated (URL1,URL2)</option>
                            <option value="tab">Tab-separated (URL1    URL2)</option>
                            <option value="line">Line-separated (URL1 on line 1, URL2 on line 2, etc.)</option>
                        </select>
                        <div class="form-text">
                            Choose how URLs are separated in your file.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="file" class="form-label">
                            <i class="fas fa-file me-1"></i>
                            Select File
                        </label>
                        <div class="file-upload-area" id="fileUploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <p class="mb-2">Drag and drop your file here, or click to browse</p>
                            <p class="text-muted small">Supported formats: .txt, .csv, .tsv (Max 16MB)</p>
                            <input type="file" class="form-control" id="file" name="file" 
                                   accept=".txt,.csv,.tsv" required style="display: none;">
                        </div>
                        <div id="fileInfo" class="mt-2" style="display: none;">
                            <small class="text-success">
                                <i class="fas fa-check-circle me-1"></i>
                                <span id="fileName"></span>
                            </small>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100" id="uploadBtn" disabled>
                        <i class="fas fa-upload me-2"></i>
                        Upload and Process
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- File Format Examples -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    File Format Examples
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="fas fa-comma me-1"></i> Comma-separated</h6>
                        <pre class="bg-light p-2 rounded"><code>url1.pdf,url2.pdf
url3.pdf,url4.pdf</code></pre>
                        <a href="{{ url_for('download_sample', delimiter='comma') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-download me-1"></i> Download Sample CSV
                        </a>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-arrows-alt-h me-1"></i> Tab-separated</h6>
                        <pre class="bg-light p-2 rounded"><code>url1.pdf	url2.pdf
url3.pdf	url4.pdf</code></pre>
                        <a href="{{ url_for('download_sample', delimiter='tab') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-download me-1"></i> Download Sample TSV
                        </a>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-list me-1"></i> Line-separated</h6>
                        <pre class="bg-light p-2 rounded"><code>url1.pdf
url2.pdf
url3.pdf
url4.pdf</code></pre>
                        <a href="{{ url_for('download_sample', delimiter='line') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-download me-1"></i> Download Sample TXT
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileUploadArea = document.getElementById('fileUploadArea');
    const fileInput = document.getElementById('file');
    const uploadBtn = document.getElementById('uploadBtn');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');

    // Click to browse
    fileUploadArea.addEventListener('click', function() {
        fileInput.click();
    });

    // File selection
    fileInput.addEventListener('change', function() {
        if (this.files.length > 0) {
            const file = this.files[0];
            fileName.textContent = file.name;
            fileInfo.style.display = 'block';
            uploadBtn.disabled = false;
        } else {
            fileInfo.style.display = 'none';
            uploadBtn.disabled = true;
        }
    });

    // Drag and drop
    fileUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });

    fileUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });

    fileUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            fileInput.dispatchEvent(new Event('change'));
        }
    });

    // Form submission loading states
    document.getElementById('manualForm').addEventListener('submit', function() {
        const btn = this.querySelector('button[type="submit"]');
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Querying...';
        btn.disabled = true;
    });

    document.getElementById('uploadForm').addEventListener('submit', function() {
        const btn = document.getElementById('uploadBtn');
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
        btn.disabled = true;
    });
});
</script>
{% endblock %}
