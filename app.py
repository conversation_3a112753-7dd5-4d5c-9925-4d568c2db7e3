from flask import Flask, render_template, request, jsonify, flash, redirect, url_for
import os
import csv
import io
from typing import List, Tuple
from database import get_db_manager, close_db_manager
from file_processor import FileProcessor
import atexit

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'  # Change this in production
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Ensure database connection is closed when app shuts down
atexit.register(close_db_manager)



@app.route('/')
def index():
    """Main page with input options"""
    return render_template('index.html')

@app.route('/manual_query', methods=['POST'])
def manual_query():
    """Handle manual URL pair input"""
    url1 = request.form.get('url1', '').strip()
    url2 = request.form.get('url2', '').strip()
    
    if not url1 or not url2:
        flash('Both URLs are required for manual query')
        return redirect(url_for('index'))
    
    try:
        db = get_db_manager()
        success, results, error_msg = db.query_url_pair(url1, url2)
        
        if not success:
            flash(f'Query failed: {error_msg}')
            return redirect(url_for('index'))
        
        # Prepare results for display
        query_data = [{
            'pair_index': 1,
            'url1': url1,
            'url2': url2,
            'success': success,
            'error_message': error_msg,
            'query_results': results
        }]
        
        return render_template('results.html', query_data=query_data, is_single_query=True)
        
    except Exception as e:
        flash(f'Database error: {str(e)}')
        return redirect(url_for('index'))

@app.route('/file_upload', methods=['POST'])
def file_upload():
    """Handle file upload and process URL pairs"""
    if 'file' not in request.files:
        flash('No file selected')
        return redirect(url_for('index'))

    file = request.files['file']
    delimiter = request.form.get('delimiter', 'auto')

    if file.filename == '':
        flash('No file selected')
        return redirect(url_for('index'))

    # Validate file using FileProcessor
    file_content_bytes = file.read()
    file.seek(0)  # Reset file pointer

    is_valid, error_msg = FileProcessor.validate_file(file.filename, len(file_content_bytes))
    if not is_valid:
        flash(f'File validation failed: {error_msg}')
        return redirect(url_for('index'))

    try:
        # Read file content
        file_content = file_content_bytes.decode('utf-8')

        # Parse URL pairs using enhanced processor
        url_pairs, warnings = FileProcessor.parse_url_pairs(file_content, delimiter)

        # Display warnings to user
        for warning in warnings:
            flash(f'Warning: {warning}')

        if not url_pairs:
            flash('No valid URL pairs found in the file')
            return redirect(url_for('index'))

        # Get file statistics
        stats = FileProcessor.get_file_stats(url_pairs)
        flash(f'Successfully processed {stats["total_pairs"]} URL pairs from file')

        # Query database for all pairs
        db = get_db_manager()
        query_data = db.query_multiple_url_pairs(url_pairs)

        return render_template('results.html', query_data=query_data, is_single_query=False, file_stats=stats)

    except UnicodeDecodeError:
        flash('Error reading file. Please ensure it is a valid text file with UTF-8 encoding.')
        return redirect(url_for('index'))
    except Exception as e:
        flash(f'Error processing file: {str(e)}')
        return redirect(url_for('index'))

@app.route('/api/query_pair', methods=['POST'])
def api_query_pair():
    """API endpoint for querying a single URL pair"""
    data = request.get_json()

    if not data or 'url1' not in data or 'url2' not in data:
        return jsonify({'error': 'Both url1 and url2 are required'}), 400

    try:
        db = get_db_manager()
        success, results, error_msg = db.query_url_pair(data['url1'], data['url2'])

        return jsonify({
            'success': success,
            'results': results,
            'error_message': error_msg
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/download_sample/<delimiter>')
def download_sample(delimiter):
    """Generate and download sample file for the specified delimiter"""
    if delimiter not in ['comma', 'tab', 'line']:
        flash('Invalid delimiter type')
        return redirect(url_for('index'))

    try:
        sample_content = FileProcessor.generate_sample_file(delimiter, num_pairs=3)

        # Set appropriate filename and content type
        filename_map = {
            'comma': 'sample_urls.csv',
            'tab': 'sample_urls.tsv',
            'line': 'sample_urls.txt'
        }

        response = app.response_class(
            sample_content,
            mimetype='text/plain',
            headers={'Content-Disposition': f'attachment; filename={filename_map[delimiter]}'}
        )

        return response

    except Exception as e:
        flash(f'Error generating sample file: {str(e)}')
        return redirect(url_for('index'))

@app.errorhandler(413)
def too_large(e):
    flash('File too large. Maximum size is 16MB.')
    return redirect(url_for('index'))

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
