# Oracle Database URL Query Tool

A Flask web application that allows users to query an Oracle database using URL pairs. The application supports both manual input and file upload for batch processing.

## Features

- **Manual Input**: Enter two URLs manually for quick single queries
- **File Upload**: Upload text files containing multiple URL pairs for batch processing
- **Multiple File Formats**: Support for comma-separated, tab-separated, and line-separated URL pairs
- **Oracle Database Integration**: Connects to Oracle database to retrieve comparison data
- **Results Display**: Shows results in a clean table format with export options
- **File Validation**: Comprehensive validation and error handling for uploaded files

## Prerequisites

- Python 3.7 or higher
- Access to Oracle database (configured in `database.py`)
- Oracle Instant Client (for oracledb package)

## Installation

1. **Clone or download the project files**

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure Oracle Database Connection**:
   - Update the database connection parameters in `database.py` if needed
   - Ensure you have the correct Oracle Instant Client installed
   - Verify database connectivity by testing the existing `my_orcale_python.py` script

4. **Run the Flask application**:
   ```bash
   python app.py
   ```

5. **Access the application**:
   - Open your web browser and navigate to `http://localhost:5000`

## Usage

### Manual Input
1. Navigate to the home page
2. Enter URL 1 and URL 2 in the manual input form
3. Click "Query Database" to execute the query
4. View results in the results page

### File Upload
1. Prepare a text file with URL pairs in one of these formats:
   - **Comma-separated**: `url1,url2` (one pair per line)
   - **Tab-separated**: `url1    url2` (one pair per line)
   - **Line-separated**: URL1 on line 1, URL2 on line 2, URL3 on line 3, URL4 on line 4, etc.

2. Upload the file using the file upload form
3. Select the appropriate delimiter or use auto-detection
4. Click "Upload and Process" to execute batch queries
5. View consolidated results for all URL pairs

### Sample Files
You can download sample files for each format from the application interface to understand the expected file structure.

## File Format Examples

### Comma-separated (.csv)
```
http://example.com/doc1.pdf,http://example.com/doc2.pdf
http://example.com/doc3.pdf,http://example.com/doc4.pdf
```

### Tab-separated (.tsv)
```
http://example.com/doc1.pdf	http://example.com/doc2.pdf
http://example.com/doc3.pdf	http://example.com/doc4.pdf
```

### Line-separated (.txt)
```
http://example.com/doc1.pdf
http://example.com/doc2.pdf
http://example.com/doc3.pdf
http://example.com/doc4.pdf
```

## Database Schema

The application queries the following Oracle database structure:

- **automation2.document@auto**: Document table
- **automation2.pdf@auto**: PDF table with URL information
- **automation2.new_compare_pdfs@auto**: Comparison results table

### Query Logic
1. For each URL, the application finds the corresponding document ID
2. Uses both document IDs to query the comparison table
3. Returns comparison results including:
   - RECID, CM_DOC_ID, REV_DOC_ID
   - VENDOR, DIFF_FILE_PATH
   - CHANGED_FEATURES, INPUT_FEATURES
   - CM_LINE_PN, REV_LINE_PN
   - FAILED_LINE_CM, FAILED_LINE_REV, FAILED_AT_STRUCTURED

## API Endpoints

### POST /api/query_pair
Query a single URL pair via API.

**Request Body**:
```json
{
    "url1": "http://example.com/doc1.pdf",
    "url2": "http://example.com/doc2.pdf"
}
```

**Response**:
```json
{
    "success": true,
    "results": [...],
    "error_message": ""
}
```

## Configuration

### Database Configuration
Update `database.py` to modify database connection parameters:
```python
dsn = oracledb.makedsn("your_host", 1521, service_name="your_service")
conn = oracledb.connect(user="your_user", password="your_password", dsn=dsn)
```

### Application Configuration
Update `app.py` for application settings:
- `app.secret_key`: Change for production use
- `app.config['MAX_CONTENT_LENGTH']`: Adjust maximum file upload size
- `host` and `port`: Modify server binding settings

## File Limitations

- Maximum file size: 16MB
- Supported formats: .txt, .csv, .tsv
- Encoding: UTF-8

## Error Handling

The application includes comprehensive error handling for:
- Database connection issues
- Invalid file formats
- URL validation errors
- File parsing errors
- Query execution errors

## Security Considerations

- Change the Flask secret key in production
- Implement proper authentication if needed
- Validate and sanitize all user inputs
- Use HTTPS in production environments
- Restrict database user permissions to read-only

## Troubleshooting

### Common Issues

1. **Oracle Connection Error**:
   - Verify Oracle Instant Client installation
   - Check database connection parameters
   - Ensure network connectivity to database

2. **File Upload Issues**:
   - Check file size (max 16MB)
   - Verify file format (.txt, .csv, .tsv)
   - Ensure UTF-8 encoding

3. **No Results Found**:
   - Verify URLs exist in the database
   - Check URL format and accessibility
   - Review database query logic

## Development

### Project Structure
```
├── app.py                 # Main Flask application
├── database.py           # Database connection and query logic
├── file_processor.py     # File processing utilities
├── requirements.txt      # Python dependencies
├── templates/            # HTML templates
│   ├── base.html        # Base template
│   ├── index.html       # Home page
│   └── results.html     # Results display
└── my_orcale_python.py  # Original Oracle connection script
```

### Adding Features
- Extend `database.py` for additional query types
- Modify templates for UI changes
- Update `file_processor.py` for new file formats
- Add new routes in `app.py` for additional functionality

## License

This project is provided as-is for internal use. Modify and distribute according to your organization's policies.
