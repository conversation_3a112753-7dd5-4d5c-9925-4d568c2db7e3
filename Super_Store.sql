SELECT * from dbo.SuperStore

select count(Category),Category, Ship_Mode  from dbo.Superstore
GROUP BY Ship_Mode, Category 


SELECT
    Category,
    Ship_Mode,
    COUNT(*) AS ShipModeCount,
    CAST(COUNT(Ship_Mode) * 100.0 / SUM(COUNT(*)) OVER (PARTITION BY Category) AS DECIMAL(5,2)) AS ShipModePercentage
FROM dbo.SuperStore
GROUP BY Category, Ship_Mode
ORDER BY Category, ShipModePercentage DESC;

select Sub_Category, SUM(Sales) OVER (PARTITION BY Sub_Category)
from dbo.Superstore

select Region, State, Sum(Profit) AS Total_profit, CAST(SUM(Profit) OVER (PARTITION BY State) *100/ Sum(Profit) AS DECIMAL(5,2)) AS ProfitContributionPct
from dbo.Superstore 
GROUP BY Region, State


SELECT Region, State, SUM(Profit)
FROM Superstore
GROUP BY Region, State

SELECT Region, State, Profit
FROM Superstore

select Region, State, SUM(Profit), CAST(SUM(Profit)*100 / SUM(SUM(Profit)) OVER (PARTITION BY Region) AS DECIMAL(5,2)) AS ProfitContributionPct
FROM Superstore
GROUP BY Region, State