#!/usr/bin/env python3
"""
Test script for the Oracle Database URL Query Tool
"""

import unittest
from unittest.mock import patch, MagicMock
import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from file_processor import FileProcessor
from database import OracleDBManager

class TestFileProcessor(unittest.TestCase):
    """Test cases for FileProcessor class"""
    
    def test_validate_file(self):
        """Test file validation"""
        # Valid files
        self.assertTrue(FileProcessor.validate_file('test.txt', 1000)[0])
        self.assertTrue(FileProcessor.validate_file('test.csv', 1000)[0])
        self.assertTrue(FileProcessor.validate_file('test.tsv', 1000)[0])
        
        # Invalid files
        self.assertFalse(FileProcessor.validate_file('test.pdf', 1000)[0])
        self.assertFalse(FileProcessor.validate_file('test.txt', 20*1024*1024)[0])  # Too large
        self.assertFalse(FileProcessor.validate_file('', 1000)[0])  # No filename
    
    def test_detect_delimiter(self):
        """Test delimiter detection"""
        # Comma-separated
        content = "url1,url2\nurl3,url4"
        self.assertEqual(FileProcessor.detect_delimiter(content), 'comma')
        
        # Tab-separated
        content = "url1\turl2\nurl3\turl4"
        self.assertEqual(FileProcessor.detect_delimiter(content), 'tab')
        
        # Line-separated
        content = "url1\nurl2\nurl3\nurl4"
        self.assertEqual(FileProcessor.detect_delimiter(content), 'line')
    
    def test_validate_url(self):
        """Test URL validation"""
        # Valid URLs
        self.assertTrue(FileProcessor.validate_url('http://example.com/test.pdf'))
        self.assertTrue(FileProcessor.validate_url('https://example.com/test.pdf'))
        self.assertTrue(FileProcessor.validate_url('http://localhost:8080/test.pdf'))
        
        # Invalid URLs
        self.assertFalse(FileProcessor.validate_url(''))
        self.assertFalse(FileProcessor.validate_url('not-a-url'))
        self.assertFalse(FileProcessor.validate_url('ftp://example.com/test.pdf'))
    
    def test_parse_url_pairs_comma(self):
        """Test parsing comma-separated URL pairs"""
        content = "http://example.com/1.pdf,http://example.com/2.pdf\nhttp://example.com/3.pdf,http://example.com/4.pdf"
        pairs, warnings = FileProcessor.parse_url_pairs(content, 'comma')
        
        self.assertEqual(len(pairs), 2)
        self.assertEqual(pairs[0], ('http://example.com/1.pdf', 'http://example.com/2.pdf'))
        self.assertEqual(pairs[1], ('http://example.com/3.pdf', 'http://example.com/4.pdf'))
    
    def test_parse_url_pairs_tab(self):
        """Test parsing tab-separated URL pairs"""
        content = "http://example.com/1.pdf\thttp://example.com/2.pdf\nhttp://example.com/3.pdf\thttp://example.com/4.pdf"
        pairs, warnings = FileProcessor.parse_url_pairs(content, 'tab')
        
        self.assertEqual(len(pairs), 2)
        self.assertEqual(pairs[0], ('http://example.com/1.pdf', 'http://example.com/2.pdf'))
        self.assertEqual(pairs[1], ('http://example.com/3.pdf', 'http://example.com/4.pdf'))
    
    def test_parse_url_pairs_line(self):
        """Test parsing line-separated URL pairs"""
        content = "http://example.com/1.pdf\nhttp://example.com/2.pdf\nhttp://example.com/3.pdf\nhttp://example.com/4.pdf"
        pairs, warnings = FileProcessor.parse_url_pairs(content, 'line')
        
        self.assertEqual(len(pairs), 2)
        self.assertEqual(pairs[0], ('http://example.com/1.pdf', 'http://example.com/2.pdf'))
        self.assertEqual(pairs[1], ('http://example.com/3.pdf', 'http://example.com/4.pdf'))
    
    def test_generate_sample_file(self):
        """Test sample file generation"""
        # Test comma format
        sample = FileProcessor.generate_sample_file('comma', 2)
        self.assertIn(',', sample)
        self.assertEqual(len(sample.split('\n')), 2)
        
        # Test tab format
        sample = FileProcessor.generate_sample_file('tab', 2)
        self.assertIn('\t', sample)
        self.assertEqual(len(sample.split('\n')), 2)
        
        # Test line format
        sample = FileProcessor.generate_sample_file('line', 2)
        self.assertEqual(len(sample.split('\n')), 4)  # 2 pairs = 4 lines
    
    def test_get_file_stats(self):
        """Test file statistics generation"""
        pairs = [
            ('http://example.com/1.pdf', 'http://example.com/2.pdf'),
            ('http://example.com/3.pdf', 'http://example.com/4.pdf'),
            ('http://example.com/1.pdf', 'http://example.com/5.pdf')  # Duplicate URL1
        ]
        
        stats = FileProcessor.get_file_stats(pairs)
        
        self.assertEqual(stats['total_pairs'], 3)
        self.assertEqual(stats['unique_url1'], 2)  # 1.pdf appears twice
        self.assertEqual(stats['unique_url2'], 3)
        self.assertEqual(stats['total_unique_urls'], 5)  # 1.pdf, 2.pdf, 3.pdf, 4.pdf, 5.pdf = 5 unique

class TestDatabaseManager(unittest.TestCase):
    """Test cases for OracleDBManager class"""
    
    @patch('database.oracledb.connect')
    @patch('database.oracledb.makedsn')
    def test_database_connection(self, mock_makedsn, mock_connect):
        """Test database connection setup"""
        mock_makedsn.return_value = "mock_dsn"
        mock_connect.return_value = MagicMock()
        
        db = OracleDBManager()
        
        mock_makedsn.assert_called_once_with("10.199.104.127", 1521, service_name="scrubbing")
        mock_connect.assert_called_once_with(user="READ_ONLY", password="READ_ONLY", dsn="mock_dsn")
    
    @patch('database.oracledb.connect')
    @patch('database.oracledb.makedsn')
    def test_get_doc_id_by_url(self, mock_makedsn, mock_connect):
        """Test document ID retrieval by URL"""
        # Mock database connection and cursor
        mock_cursor = MagicMock()
        mock_cursor.fetchone.return_value = [12345]
        mock_conn = MagicMock()
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_connect.return_value = mock_conn
        
        db = OracleDBManager()
        doc_id = db.get_doc_id_by_url('http://example.com/test.pdf')
        
        self.assertEqual(doc_id, 12345)
        mock_cursor.execute.assert_called_once()
    
    @patch('database.oracledb.connect')
    @patch('database.oracledb.makedsn')
    def test_query_url_pair_success(self, mock_makedsn, mock_connect):
        """Test successful URL pair query"""
        # Mock database responses
        mock_cursor = MagicMock()
        mock_cursor.fetchone.side_effect = [[12345], [67890]]  # Two doc IDs
        mock_cursor.description = [('RECID',), ('CM_DOC_ID',), ('REV_DOC_ID',)]
        mock_cursor.__iter__.return_value = [
            (1, 12345, 67890),
            (2, 12345, 67890)
        ]
        
        mock_conn = MagicMock()
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_connect.return_value = mock_conn
        
        db = OracleDBManager()
        success, results, error_msg = db.query_url_pair('http://example.com/1.pdf', 'http://example.com/2.pdf')
        
        self.assertTrue(success)
        self.assertEqual(len(results), 2)
        self.assertEqual(error_msg, "")
    
    @patch('database.oracledb.connect')
    @patch('database.oracledb.makedsn')
    def test_query_url_pair_not_found(self, mock_makedsn, mock_connect):
        """Test URL pair query when document not found"""
        # Mock database responses - first URL not found
        mock_cursor = MagicMock()
        mock_cursor.fetchone.return_value = None  # No document found
        
        mock_conn = MagicMock()
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_connect.return_value = mock_conn
        
        db = OracleDBManager()
        success, results, error_msg = db.query_url_pair('http://example.com/notfound.pdf', 'http://example.com/2.pdf')
        
        self.assertFalse(success)
        self.assertEqual(len(results), 0)
        self.assertIn("Document not found", error_msg)

def run_tests():
    """Run all tests"""
    print("Running tests for Oracle Database URL Query Tool...")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestFileProcessor))
    suite.addTests(loader.loadTestsFromTestCase(TestDatabaseManager))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✓ All tests passed!")
    else:
        print(f"✗ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
