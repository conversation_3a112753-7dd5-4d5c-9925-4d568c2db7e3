import oracledb
from typing import List, Dict, Optional, Tuple

class OracleDBManager:
    """
    Database manager that extends the existing Oracle connection functionality
    for handling URL pair queries in the Flask application.
    """
    
    def __init__(self):
        # Build DSN using the same configuration as my_orcale_python.py
        self.dsn = oracledb.makedsn("10.199.104.127", 1521, service_name="scrubbing")
        self.conn = None
        self.connect()
    
    def connect(self):
        """Establish connection to Oracle database"""
        try:
            self.conn = oracledb.connect(user="READ_ONLY", password="READ_ONLY", dsn=self.dsn)
            print("Successfully connected to Oracle database")
        except Exception as e:
            print(f"Error connecting to database: {e}")
            raise
    
    def disconnect(self):
        """Close database connection"""
        if self.conn:
            self.conn.close()
            print("Database connection closed")
    
    def get_doc_id_by_url(self, url: str) -> Optional[int]:
        """
        Get document ID by URL - same function as in my_orcale_python.py
        
        Args:
            url: The URL to search for
            
        Returns:
            Document ID if found, None otherwise
        """
        try:
            with self.conn.cursor() as cur:
                cur.execute("""
                    SELECT doc.id
                    FROM automation2.document@auto doc
                    JOIN automation2.pdf@auto pdf ON doc.pdf_id = pdf.id
                    WHERE pdf.se_url = :url
                """, [url])
                row = cur.fetchone()
                return row[0] if row else None
        except Exception as e:
            print(f"Error getting document ID for URL {url}: {e}")
            return None
    
    def query_url_pair(self, url1: str, url2: str) -> Tuple[bool, List[Dict], str]:
        """
        Query comparison data for a pair of URLs
        
        Args:
            url1: First URL
            url2: Second URL
            
        Returns:
            Tuple of (success, results_list, error_message)
        """
        try:
            # Get document IDs for both URLs
            doc1 = self.get_doc_id_by_url(url1)
            doc2 = self.get_doc_id_by_url(url2)
            
            if not doc1:
                return False, [], f"Document not found for URL1: {url1}"
            if not doc2:
                return False, [], f"Document not found for URL2: {url2}"
            
            # Query comparison data
            with self.conn.cursor() as cur:
                cur.execute("""
                    SELECT RECID, CM_DOC_ID, REV_DOC_ID, VENDOR, DIFF_FILE_PATH, 
                           CHANGED_FEATURES, INPUT_FEATURES, CM_LINE_PN, REV_LINE_PN, 
                           FAILED_LINE_CM, FAILED_LINE_REV, FAILED_AT_STRUCTURED
                    FROM automation2.new_compare_pdfs@auto
                    WHERE rev_doc_id = :doc1
                      AND cm_doc_id = :doc2
                """, [doc1, doc2])
                
                # Get column names
                columns = [desc[0] for desc in cur.description]
                
                # Fetch all results and convert to list of dictionaries
                results = []
                for row in cur:
                    row_dict = dict(zip(columns, row))
                    results.append(row_dict)
                
                return True, results, ""
                
        except Exception as e:
            error_msg = f"Error querying URL pair ({url1}, {url2}): {e}"
            print(error_msg)
            return False, [], error_msg
    
    def query_multiple_url_pairs(self, url_pairs: List[Tuple[str, str]]) -> List[Dict]:
        """
        Query multiple URL pairs and return consolidated results
        
        Args:
            url_pairs: List of (url1, url2) tuples
            
        Returns:
            List of dictionaries containing results for each pair
        """
        all_results = []
        
        for i, (url1, url2) in enumerate(url_pairs):
            success, results, error_msg = self.query_url_pair(url1, url2)
            
            pair_result = {
                'pair_index': i + 1,
                'url1': url1,
                'url2': url2,
                'success': success,
                'error_message': error_msg,
                'query_results': results
            }
            
            all_results.append(pair_result)
        
        return all_results
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()

# Global database manager instance
db_manager = None

def get_db_manager():
    """Get or create database manager instance"""
    global db_manager
    if db_manager is None:
        db_manager = OracleDBManager()
    return db_manager

def close_db_manager():
    """Close database manager connection"""
    global db_manager
    if db_manager:
        db_manager.disconnect()
        db_manager = None
