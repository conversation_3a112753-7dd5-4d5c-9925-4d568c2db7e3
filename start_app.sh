#!/bin/bash

echo "================================================"
echo "Oracle Database URL Query Tool"
echo "================================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.7 or higher"
    exit 1
fi

# Check if pip is available
if ! command -v pip3 &> /dev/null; then
    echo "Error: pip3 is not available"
    echo "Please ensure pip is installed with Python"
    exit 1
fi

echo "Installing/updating dependencies..."
pip3 install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "Error: Failed to install dependencies"
    exit 1
fi

echo
echo "Starting the application..."
echo "Access the application at: http://localhost:5000"
echo "Press Ctrl+C to stop the server"
echo

python3 run.py
