import csv
import io
from typing import List, <PERSON><PERSON>, Dict, Optional
import re

class FileProcessor:
    """
    Utility class for processing uploaded files containing URL pairs
    """
    
    SUPPORTED_EXTENSIONS = ['.txt', '.csv', '.tsv']
    MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB
    
    @staticmethod
    def validate_file(filename: str, file_size: int) -> <PERSON><PERSON>[bool, str]:
        """
        Validate uploaded file
        
        Args:
            filename: Name of the uploaded file
            file_size: Size of the file in bytes
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not filename:
            return False, "No filename provided"
        
        # Check file extension
        file_ext = '.' + filename.lower().split('.')[-1] if '.' in filename else ''
        if file_ext not in FileProcessor.SUPPORTED_EXTENSIONS:
            return False, f"Unsupported file type. Supported: {', '.join(FileProcessor.SUPPORTED_EXTENSIONS)}"
        
        # Check file size
        if file_size > FileProcessor.MAX_FILE_SIZE:
            return False, f"File too large. Maximum size: {FileProcessor.MAX_FILE_SIZE // (1024*1024)}MB"
        
        return True, ""
    
    @staticmethod
    def detect_delimiter(content: str) -> str:
        """
        Auto-detect delimiter in file content
        
        Args:
            content: File content as string
            
        Returns:
            Detected delimiter type ('comma', 'tab', or 'line')
        """
        lines = content.strip().split('\n')
        if not lines:
            return 'line'
        
        first_line = lines[0]
        
        # Count delimiters in first line
        comma_count = first_line.count(',')
        tab_count = first_line.count('\t')
        
        # If we have exactly one comma or tab, it's likely the delimiter
        if tab_count == 1 and comma_count == 0:
            return 'tab'
        elif comma_count == 1 and tab_count == 0:
            return 'comma'
        elif comma_count > 1 or tab_count > 1:
            # Multiple delimiters, prefer tab over comma
            return 'tab' if tab_count > 0 else 'comma'
        else:
            # No clear delimiter, assume line-separated
            return 'line'
    
    @staticmethod
    def validate_url(url: str) -> bool:
        """
        Basic URL validation
        
        Args:
            url: URL string to validate
            
        Returns:
            True if URL appears valid, False otherwise
        """
        if not url or len(url.strip()) == 0:
            return False
        
        url = url.strip()
        
        # Basic URL pattern check
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        return bool(url_pattern.match(url))
    
    @staticmethod
    def parse_url_pairs(content: str, delimiter: str = 'auto') -> Tuple[List[Tuple[str, str]], List[str]]:
        """
        Parse URL pairs from file content with detailed error reporting
        
        Args:
            content: File content as string
            delimiter: Delimiter type ('comma', 'tab', 'line', or 'auto')
            
        Returns:
            Tuple of (url_pairs_list, warnings_list)
        """
        url_pairs = []
        warnings = []
        
        if not content.strip():
            warnings.append("File is empty")
            return url_pairs, warnings
        
        lines = content.strip().split('\n')
        
        # Auto-detect delimiter if needed
        if delimiter == 'auto':
            delimiter = FileProcessor.detect_delimiter(content)
            warnings.append(f"Auto-detected delimiter: {delimiter}")
        
        if delimiter in ['comma', 'tab']:
            sep = ',' if delimiter == 'comma' else '\t'
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue  # Skip empty lines
                
                # Handle CSV parsing for comma-separated values
                if delimiter == 'comma':
                    try:
                        reader = csv.reader([line])
                        parts = next(reader)
                    except csv.Error:
                        warnings.append(f"Line {line_num}: CSV parsing error")
                        continue
                else:
                    parts = line.split(sep)
                
                if len(parts) < 2:
                    warnings.append(f"Line {line_num}: Less than 2 URLs found")
                    continue
                elif len(parts) > 2:
                    warnings.append(f"Line {line_num}: More than 2 URLs found, using first 2")
                
                url1 = parts[0].strip()
                url2 = parts[1].strip()
                
                # Validate URLs
                if not FileProcessor.validate_url(url1):
                    warnings.append(f"Line {line_num}: Invalid URL1 format")
                    continue
                if not FileProcessor.validate_url(url2):
                    warnings.append(f"Line {line_num}: Invalid URL2 format")
                    continue
                
                url_pairs.append((url1, url2))
        
        elif delimiter == 'line':
            # Consecutive lines form pairs
            for i in range(0, len(lines), 2):
                if i + 1 >= len(lines):
                    warnings.append(f"Line {i+1}: Odd number of lines, last line ignored")
                    break
                
                url1 = lines[i].strip()
                url2 = lines[i + 1].strip()
                
                if not url1 or not url2:
                    warnings.append(f"Lines {i+1}-{i+2}: Empty URL(s) found")
                    continue
                
                # Validate URLs
                if not FileProcessor.validate_url(url1):
                    warnings.append(f"Line {i+1}: Invalid URL format")
                    continue
                if not FileProcessor.validate_url(url2):
                    warnings.append(f"Line {i+2}: Invalid URL format")
                    continue
                
                url_pairs.append((url1, url2))
        
        return url_pairs, warnings
    
    @staticmethod
    def generate_sample_file(delimiter: str, num_pairs: int = 3) -> str:
        """
        Generate sample file content for demonstration
        
        Args:
            delimiter: Type of delimiter to use
            num_pairs: Number of URL pairs to generate
            
        Returns:
            Sample file content as string
        """
        base_urls = [
            "http://download.siliconexpert.com/pdfs2/2025/6/17/11/34/37/1241618801/ech_/manual/skupage.014626.pdf",
            "http://download.siliconexpert.com/pdfs2/2025/6/17/11/34/37/1241618802/ech_/manual/skupage.014627.pdf",
            "http://download.siliconexpert.com/pdfs2/2025/6/17/11/34/37/1241618803/ech_/manual/skupage.014628.pdf",
            "http://download.siliconexpert.com/pdfs2/2025/6/17/11/34/37/1241618804/ech_/manual/skupage.014629.pdf",
            "http://download.siliconexpert.com/pdfs2/2025/6/17/11/34/37/1241618805/ech_/manual/skupage.014630.pdf",
            "http://download.siliconexpert.com/pdfs2/2025/6/17/11/34/37/1241618806/ech_/manual/skupage.014631.pdf"
        ]
        
        content = ""
        
        if delimiter == 'comma':
            for i in range(min(num_pairs, len(base_urls) // 2)):
                content += f"{base_urls[i*2]},{base_urls[i*2+1]}\n"
        elif delimiter == 'tab':
            for i in range(min(num_pairs, len(base_urls) // 2)):
                content += f"{base_urls[i*2]}\t{base_urls[i*2+1]}\n"
        elif delimiter == 'line':
            for i in range(min(num_pairs, len(base_urls) // 2)):
                content += f"{base_urls[i*2]}\n{base_urls[i*2+1]}\n"
        
        return content.strip()
    
    @staticmethod
    def get_file_stats(url_pairs: List[Tuple[str, str]]) -> Dict[str, int]:
        """
        Get statistics about parsed URL pairs
        
        Args:
            url_pairs: List of URL pairs
            
        Returns:
            Dictionary with statistics
        """
        stats = {
            'total_pairs': len(url_pairs),
            'unique_url1': len(set(pair[0] for pair in url_pairs)),
            'unique_url2': len(set(pair[1] for pair in url_pairs)),
            'total_unique_urls': len(set(url for pair in url_pairs for url in pair))
        }
        
        return stats
