{% extends "base.html" %}

{% block title %}Query Results - Oracle URL Query Tool{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-table me-2"></i>
                Query Results
            </h1>
            <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>
                Back to Query
            </a>
        </div>
    </div>
</div>

{% if query_data %}
    <!-- Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h4 class="text-primary">{{ query_data|length }}</h4>
                            <p class="text-muted mb-0">URL Pairs Processed</p>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-success">
                                {{ query_data | selectattr('success') | list | length }}
                            </h4>
                            <p class="text-muted mb-0">Successful Queries</p>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-danger">
                                {{ query_data | rejectattr('success') | list | length }}
                            </h4>
                            <p class="text-muted mb-0">Failed Queries</p>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-info">
                                {% set total_results = query_data | map(attribute='query_results') | map('length') | sum %}
                                {{ total_results }}
                            </h4>
                            <p class="text-muted mb-0">Total Records Found</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results for each URL pair -->
    {% for pair_data in query_data %}
        <div class="card mb-4">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="mb-0">
                            {% if is_single_query %}
                                <i class="fas fa-search me-2"></i>
                                Manual Query Result
                            {% else %}
                                <i class="fas fa-list-ol me-2"></i>
                                Pair {{ pair_data.pair_index }}
                            {% endif %}
                            {% if pair_data.success %}
                                <span class="badge bg-success ms-2">Success</span>
                            {% else %}
                                <span class="badge bg-danger ms-2">Failed</span>
                            {% endif %}
                        </h5>
                    </div>
                    <div class="col-md-4 text-end">
                        {% if pair_data.query_results %}
                            <small class="text-muted">
                                {{ pair_data.query_results|length }} record(s) found
                            </small>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <!-- URL Information -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>URL 1:</strong>
                        <div class="url-cell text-break">
                            <a href="{{ pair_data.url1 }}" target="_blank" class="text-decoration-none">
                                {{ pair_data.url1 }}
                                <i class="fas fa-external-link-alt ms-1"></i>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <strong>URL 2:</strong>
                        <div class="url-cell text-break">
                            <a href="{{ pair_data.url2 }}" target="_blank" class="text-decoration-none">
                                {{ pair_data.url2 }}
                                <i class="fas fa-external-link-alt ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>

                {% if not pair_data.success %}
                    <!-- Error Message -->
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Error:</strong> {{ pair_data.error_message }}
                    </div>
                {% elif not pair_data.query_results %}
                    <!-- No Results -->
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No comparison data found for this URL pair.
                    </div>
                {% else %}
                    <!-- Query Results Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    {% for column in pair_data.query_results[0].keys() %}
                                        <th>{{ column }}</th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for row in pair_data.query_results %}
                                    <tr>
                                        {% for value in row.values() %}
                                            <td>
                                                {% if value is none %}
                                                    <span class="text-muted">NULL</span>
                                                {% elif value is string and value.startswith('http') %}
                                                    <a href="{{ value }}" target="_blank" class="text-decoration-none">
                                                        {{ value[:50] }}{% if value|length > 50 %}...{% endif %}
                                                        <i class="fas fa-external-link-alt ms-1"></i>
                                                    </a>
                                                {% else %}
                                                    {{ value }}
                                                {% endif %}
                                            </td>
                                        {% endfor %}
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% endif %}
            </div>
        </div>
    {% endfor %}

    <!-- Export Options -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    <h6 class="card-title">Export Results</h6>
                    <button class="btn btn-outline-success me-2" onclick="exportToCSV()">
                        <i class="fas fa-file-csv me-2"></i>
                        Export to CSV
                    </button>
                    <button class="btn btn-outline-info" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>
                        Print Results
                    </button>
                </div>
            </div>
        </div>
    </div>

{% else %}
    <div class="alert alert-warning text-center">
        <i class="fas fa-exclamation-triangle me-2"></i>
        No query data available.
    </div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
function exportToCSV() {
    const tables = document.querySelectorAll('.table-responsive table');
    let csvContent = '';
    
    tables.forEach((table, index) => {
        if (index > 0) csvContent += '\n\n';
        
        // Add pair header
        const pairHeader = table.closest('.card').querySelector('.card-header h5').textContent.trim();
        csvContent += pairHeader + '\n';
        
        // Add table data
        const rows = table.querySelectorAll('tr');
        rows.forEach(row => {
            const cells = row.querySelectorAll('th, td');
            const rowData = Array.from(cells).map(cell => {
                let text = cell.textContent.trim();
                // Escape quotes and wrap in quotes if contains comma
                if (text.includes(',') || text.includes('"')) {
                    text = '"' + text.replace(/"/g, '""') + '"';
                }
                return text;
            });
            csvContent += rowData.join(',') + '\n';
        });
    });
    
    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'oracle_query_results_' + new Date().toISOString().slice(0, 10) + '.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

// Auto-refresh for long-running queries (if needed)
document.addEventListener('DOMContentLoaded', function() {
    // Add any additional JavaScript functionality here
    console.log('Results page loaded successfully');
});
</script>
{% endblock %}
